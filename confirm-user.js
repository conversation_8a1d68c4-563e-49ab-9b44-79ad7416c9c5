#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to manually confirm a user for testing
import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = process.env.SUPABASE_URL || 'https://hrdjfukhzbzksqaupqie.supabase.co';
const SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);

async function confirmUser(email) {
  try {
    console.log(`Attempting to confirm user: ${email}`);
    
    // First, get the user by email
    const { data: users, error: getUserError } = await supabase.auth.admin.listUsers();
    
    if (getUserError) {
      console.error('Error listing users:', getUserError);
      return;
    }
    
    const user = users.users.find(u => u.email === email);
    if (!user) {
      console.error('User not found:', email);
      return;
    }
    
    console.log('Found user:', user.id, user.email, 'confirmed:', user.email_confirmed_at);
    
    // Confirm the user
    const { data, error } = await supabase.auth.admin.updateUserById(user.id, {
      email_confirm: true
    });
    
    if (error) {
      console.error('Error confirming user:', error);
      return;
    }
    
    console.log('User confirmed successfully:', data.user.email);
    
  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

// Get email from command line argument
const email = process.argv[2];
if (!email) {
  console.error('Usage: node confirm-user.js <email>');
  process.exit(1);
}

confirmUser(email);
