// Production-ready configuration using environment variables
// Fallback values should only be used for development

export const supabaseConfig = {
  url: process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || "",
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || "",
  anonKey: process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || "",
  dbPassword: process.env.VITE_DATABASE_PASSWORD || "",
};

// Validate required environment variables in production
if (process.env.NODE_ENV === 'production') {
  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);

  if (missingVars.length > 0) {
    console.error('❌ Missing required environment variables for production:', missingVars);
    console.error('Please set these environment variables before starting the server.');
    process.exit(1);
  }
}

// Validation - only check essential config
if (!supabaseConfig.url || !supabaseConfig.serviceRoleKey || !supabaseConfig.anonKey) {
  console.error(
    "❌ Error: Supabase URL, Service Role Key, and Anonymous Key must be configured."
  );
  console.error("Current config:", {
    url: supabaseConfig.url ? "✓ Set" : "✗ Missing",
    serviceRoleKey: supabaseConfig.serviceRoleKey ? "✓ Set" : "✗ Missing",
    anonKey: supabaseConfig.anonKey ? "✓ Set" : "✗ Missing",
  });
  process.exit(1);
}

// Log configuration status
console.log("✓ Supabase configuration loaded successfully");
console.log("✓ URL:", supabaseConfig.url ? "Configured" : "Missing");
console.log(
  "✓ Service Role Key:",
  supabaseConfig.serviceRoleKey ? "Configured" : "Missing"
);
console.log(
  "✓ Anonymous Key:",
  supabaseConfig.anonKey ? "Configured" : "Missing"
);
console.log(
  "✓ Database Password:",
  supabaseConfig.dbPassword
    ? "Configured"
    : "Optional - using service role key authentication"
);
