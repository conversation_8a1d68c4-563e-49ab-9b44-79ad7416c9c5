import { Context, Next } from 'hono';
import { createClient } from '@supabase/supabase-js';
import { supabaseConfig } from '../config';

// Create Supabase clients outside the middleware to avoid creating new clients on each request
const supabaseUrl = supabaseConfig.url;
const supabaseServiceKey = supabaseConfig.serviceRoleKey;
const supabaseAnonKey = supabaseConfig.anonKey;

console.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');
console.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');
console.log('Using Supabase Anon Key:', supabaseAnonKey ? '✓ Found' : '✗ Not found');

// Create two Supabase client instances:
// 1. Service role client for admin operations (database queries, bypassing RLS)
export const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);

// 2. Anonymous client for JWT token validation (user authentication)
export const supabaseAuthClient = createClient(supabaseUrl, supabaseAnonKey);

// Middleware to attach both Supabase clients to the Hono context
export const supabaseMiddleware = async (c: Context, next: Next) => {
  if (!supabaseClient || !supabaseAuthClient) {
    console.error('Supabase client initialization failed');
    return c.json({
      error: 'Internal Server Configuration Error: Supabase client initialization failed.'
    }, 500);
  }

  // Attach both Supabase clients to the context
  c.set('supabase', supabaseClient); // For database operations
  c.set('supabaseAuth', supabaseAuthClient); // For JWT token validation

  // Log that the middleware is working (without sensitive headers)
  console.log(`Supabase clients attached to request context for ${c.req.method} ${c.req.url}`);

  await next();
};
