{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/lib/storage.ts"}, "originalCode": "import { Flashcard, FlashcardDeck, Document, StudyStats } from \"@/types\";\nimport localforage from \"localforage\";\nimport axios from \"axios\";\n\n// API base URL - handle both development and production environments\n// In production, use relative path (same origin)\n// In development, use localhost:5000\nconst API_BASE_URL = import.meta.env.PROD\n  ? \"/api\"  // Production: relative path (same origin)\n  : import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";  // Development: localhost\n\n// Initialize localforage instances for different data types\nconst documentsStore = localforage.createInstance({ name: \"documents\" });\nconst decksStore = localforage.createInstance({ name: \"flashcardDecks\" });\nconst flashcardsStore = localforage.createInstance({ name: \"flashcards\" });\n\n// Helper to get authorization token\nconst getAuthToken = async (): Promise<string | null> => {\n  try {\n    // Retrieve from localStorage or wherever your app stores the token\n    return localStorage.getItem(\"auth_token\");\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n};\n\n// Helper to set up axios headers with auth token\nconst getAuthHeaders = async () => {\n  const token = await getAuthToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n// Document Local Storage Functions (for caching and offline support)\nexport async function cacheDocument(document: Document): Promise<Document> {\n  await documentsStore.setItem(document.id, document);\n  return document;\n}\n\nexport async function getCachedDocument(id: string): Promise<Document | null> {\n  return documentsStore.getItem<Document>(id);\n}\n\nexport async function getCachedDocuments(): Promise<Document[]> {\n  const documents: Document[] = [];\n  await documentsStore.iterate<Document, void>((doc) => {\n    documents.push(doc);\n  });\n  // Sort by creation date, newest first\n  return documents.sort((a, b) => b.createdAt - a.createdAt);\n}\n\nexport async function removeCachedDocument(id: string): Promise<void> {\n  await documentsStore.removeItem(id);\n}\n\nexport async function syncDocumentsToCache(\n  documents: Document[]\n): Promise<void> {\n  for (const doc of documents) {\n    await documentsStore.setItem(doc.id, doc);\n  }\n}\n\n// Flashcard Decks API\nexport async function saveDeck(deck: FlashcardDeck): Promise<FlashcardDeck> {\n  await decksStore.setItem(deck.id, deck);\n  return deck;\n}\n\nexport async function getDeck(id: string): Promise<FlashcardDeck | null> {\n  return decksStore.getItem<FlashcardDeck>(id);\n}\n\nexport async function getAllDecks(): Promise<FlashcardDeck[]> {\n  const decks: FlashcardDeck[] = [];\n  await decksStore.iterate<FlashcardDeck, void>((deck) => {\n    decks.push(deck);\n  });\n  // Sort by creation date, newest first\n  return decks.sort((a, b) => b.createdAt - a.createdAt);\n}\n\nexport async function deleteDeck(id: string): Promise<void> {\n  await decksStore.removeItem(id);\n\n  // Also delete all flashcards in this deck\n  const allCards = await getFlashcardsByDeck(id);\n  for (const card of allCards) {\n    await deleteFlashcard(card.id);\n  }\n}\n\nexport async function updateDeckStats(\n  deckId: string\n): Promise<FlashcardDeck | null> {\n  const deck = await getDeck(deckId);\n  if (!deck) return null;\n\n  const cards = await getFlashcardsByDeck(deckId);\n  const now = Date.now();\n\n  const dueTodayCount = cards.filter((card) => {\n    return card.nextReview ? card.nextReview <= now : false;\n  }).length;\n\n  const masteredCount = cards.filter((card) => {\n    return card.interval && card.interval >= 30 * 24 * 60 * 60 * 1000; // 30 days\n  }).length;\n\n  const updatedDeck: FlashcardDeck = {\n    ...deck,\n    totalCards: cards.length,\n    dueTodayCount,\n    masteredCount,\n  };\n\n  return saveDeck(updatedDeck);\n}\n\n// Flashcards API\nexport async function saveFlashcard(card: Flashcard): Promise<Flashcard> {\n  await flashcardsStore.setItem(card.id, card);\n  await updateDeckStats(card.deckId);\n  return card;\n}\n\n/**\n * Save a batch of flashcards and create/update a deck for them\n */\nexport async function saveFlashcards(\n  cards: Flashcard[],\n  documentId: string\n): Promise<Flashcard[]> {\n  if (!cards.length) return [];\n\n  // Create a deck for these flashcards if it doesn't exist\n  const deckId = crypto.randomUUID();\n  const document = await getCachedDocument(documentId);\n  const deckName = document\n    ? `Flashcards: ${document.name}`\n    : `Flashcards ${new Date().toLocaleDateString()}`;\n\n  const deck: FlashcardDeck = {\n    id: deckId,\n    name: deckName,\n    documentId,\n    description: document\n      ? `Generated from ${document.name}`\n      : \"Generated flashcards\",\n    createdAt: Date.now(),\n    totalCards: cards.length,\n    dueTodayCount: cards.length,\n    masteredCount: 0,\n  };\n\n  await saveDeck(deck);\n\n  // Save each flashcard with the deck ID\n  const savedCards: Flashcard[] = [];\n  for (const card of cards) {\n    const flashcard: Flashcard = {\n      ...card,\n      deckId,\n      createdAt: Date.now(),\n    };\n    await saveFlashcard(flashcard);\n    savedCards.push(flashcard);\n  }\n\n  return savedCards;\n}\n\nexport async function getFlashcard(id: string): Promise<Flashcard | null> {\n  return flashcardsStore.getItem<Flashcard>(id);\n}\n\nexport async function getFlashcardsByDeck(\n  deckId: string\n): Promise<Flashcard[]> {\n  const cards: Flashcard[] = [];\n  await flashcardsStore.iterate<Flashcard, void>((card) => {\n    if (card.deckId === deckId) {\n      cards.push(card);\n    }\n  });\n\n  return cards;\n}\n\nexport async function getDueFlashcards(deckId: string): Promise<Flashcard[]> {\n  const now = Date.now();\n  const cards = await getFlashcardsByDeck(deckId);\n  return cards.filter((card) => {\n    return !card.nextReview || card.nextReview <= now;\n  });\n}\n\nexport async function exportQuizzesAsJSON(): Promise<string> {\n  const response = await fetch('/api/quizzes/export?format=json', {\n    headers: {\n      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.error || 'Failed to export quizzes');\n  }\n\n  return response.text();\n}\n\nexport async function exportQuizzesAsCSV(): Promise<string> {\n  const response = await fetch('/api/quizzes/export?format=csv', {\n    headers: {\n      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.error || 'Failed to export quizzes');\n  }\n\n  return response.text();\n}\n\n// Document comparison functions\nexport async function deleteFlashcard(id: string): Promise<void> {\n  const card = await getFlashcard(id);\n  if (card) {\n    await flashcardsStore.removeItem(id);\n    await updateDeckStats(card.deckId);\n  }\n}\n\n// Study Statistics\nexport async function getStudyStats(): Promise<StudyStats> {\n  const documents = await getCachedDocuments();\n  const decks = await getAllDecks();\n\n  let totalFlashcards = 0;\n  let dueToday = 0;\n  let totalCorrect = 0;\n  let totalReviews = 0;\n\n  for (const deck of decks) {\n    totalFlashcards += deck.totalCards;\n    dueToday += deck.dueTodayCount;\n\n    const cards = await getFlashcardsByDeck(deck.id);\n    for (const card of cards) {\n      totalCorrect += card.correct || 0;\n      totalReviews += (card.correct || 0) + (card.incorrect || 0);\n    }\n  }\n\n  const accuracy = totalReviews > 0 ? (totalCorrect / totalReviews) * 100 : 0;\n\n  return {\n    totalDocuments: documents.length,\n    totalFlashcards,\n    dueToday,\n    accuracy,\n  };\n}\n\n// Export functionality\nexport async function exportFlashcardsAsJSON(): Promise<string> {\n  const decks = await getAllDecks();\n  const allData: {\n    decks: FlashcardDeck[];\n    flashcards: Record<string, Flashcard[]>;\n  } = {\n    decks: decks,\n    flashcards: {},\n  };\n\n  for (const deck of decks) {\n    const cards = await getFlashcardsByDeck(deck.id);\n    allData.flashcards[deck.id] = cards;\n  }\n\n  return JSON.stringify(allData, null, 2);\n}\n\nexport async function exportFlashcardsAsCSV(): Promise<string> {\n  const decks = await getAllDecks();\n  let csv =\n    \"Deck,Question,Answer,Created,Last Reviewed,Next Review,Interval,Easiness Factor\\n\";\n\n  for (const deck of decks) {\n    const cards = await getFlashcardsByDeck(deck.id);\n    for (const card of cards) {\n      const row = [\n        `\"${escapeCsvField(deck.name)}\"`,\n        `\"${escapeCsvField(card.question)}\"`,\n        `\"${escapeCsvField(card.answer)}\"`,\n        new Date(card.createdAt).toISOString(),\n        card.lastReviewed ? new Date(card.lastReviewed).toISOString() : \"\",\n        card.nextReview ? new Date(card.nextReview).toISOString() : \"\",\n        card.interval ? Math.round(card.interval / (24 * 60 * 60 * 1000)) : \"\",\n        card.easinessFactor || \"\",\n      ];\n      csv += row.join(\",\") + \"\\n\";\n    }\n  }\n\n  return csv;\n}\n\nfunction escapeCsvField(field: string): string {\n  return field.replace(/\"/g, '\"\"');\n}\n\n// Clear all data\nexport async function clearAllData(): Promise<void> {\n  await documentsStore.clear();\n  await decksStore.clear();\n  await flashcardsStore.clear();\n}\n", "modifiedCode": "import { Flashcard, FlashcardDeck, Document, StudyStats } from \"@/types\";\nimport localforage from \"localforage\";\nimport axios from \"axios\";\n\n// API base URL - handle both development and production environments\n// In production, use relative path (same origin)\n// In development, use localhost:5000\nconst API_BASE_URL = import.meta.env.PROD\n  ? \"/api\"  // Production: relative path (same origin)\n  : import.meta.env.VITE_API_BASE_URL || \"http://localhost:5000/api\";  // Development: localhost\n\n// Initialize localforage instances for different data types\nconst documentsStore = localforage.createInstance({ name: \"documents\" });\nconst decksStore = localforage.createInstance({ name: \"flashcardDecks\" });\nconst flashcardsStore = localforage.createInstance({ name: \"flashcards\" });\n\n// Helper to get authorization token\nconst getAuthToken = async (): Promise<string | null> => {\n  try {\n    // Retrieve from localStorage or wherever your app stores the token\n    return localStorage.getItem(\"auth_token\");\n  } catch (error) {\n    console.error(\"Error getting auth token:\", error);\n    return null;\n  }\n};\n\n// Helper to set up axios headers with auth token\nconst getAuthHeaders = async () => {\n  const token = await getAuthToken();\n  return token ? { Authorization: `Bearer ${token}` } : {};\n};\n\n// Document Local Storage Functions (for caching and offline support)\nexport async function cacheDocument(document: Document): Promise<Document> {\n  await documentsStore.setItem(document.id, document);\n  return document;\n}\n\nexport async function getCachedDocument(id: string): Promise<Document | null> {\n  return documentsStore.getItem<Document>(id);\n}\n\nexport async function getCachedDocuments(): Promise<Document[]> {\n  const documents: Document[] = [];\n  await documentsStore.iterate<Document, void>((doc) => {\n    documents.push(doc);\n  });\n  // Sort by creation date, newest first\n  return documents.sort((a, b) => b.createdAt - a.createdAt);\n}\n\nexport async function removeCachedDocument(id: string): Promise<void> {\n  await documentsStore.removeItem(id);\n}\n\nexport async function syncDocumentsToCache(\n  documents: Document[]\n): Promise<void> {\n  for (const doc of documents) {\n    await documentsStore.setItem(doc.id, doc);\n  }\n}\n\n// Flashcard Decks API\nexport async function saveDeck(deck: FlashcardDeck): Promise<FlashcardDeck> {\n  await decksStore.setItem(deck.id, deck);\n  return deck;\n}\n\nexport async function getDeck(id: string): Promise<FlashcardDeck | null> {\n  return decksStore.getItem<FlashcardDeck>(id);\n}\n\nexport async function getAllDecks(): Promise<FlashcardDeck[]> {\n  const decks: FlashcardDeck[] = [];\n  await decksStore.iterate<FlashcardDeck, void>((deck) => {\n    decks.push(deck);\n  });\n  // Sort by creation date, newest first\n  return decks.sort((a, b) => b.createdAt - a.createdAt);\n}\n\nexport async function deleteDeck(id: string): Promise<void> {\n  await decksStore.removeItem(id);\n\n  // Also delete all flashcards in this deck\n  const allCards = await getFlashcardsByDeck(id);\n  for (const card of allCards) {\n    await deleteFlashcard(card.id);\n  }\n}\n\nexport async function updateDeckStats(\n  deckId: string\n): Promise<FlashcardDeck | null> {\n  const deck = await getDeck(deckId);\n  if (!deck) return null;\n\n  const cards = await getFlashcardsByDeck(deckId);\n  const now = Date.now();\n\n  const dueTodayCount = cards.filter((card) => {\n    return card.nextReview ? card.nextReview <= now : false;\n  }).length;\n\n  const masteredCount = cards.filter((card) => {\n    return card.interval && card.interval >= 30 * 24 * 60 * 60 * 1000; // 30 days\n  }).length;\n\n  const updatedDeck: FlashcardDeck = {\n    ...deck,\n    totalCards: cards.length,\n    dueTodayCount,\n    masteredCount,\n  };\n\n  return saveDeck(updatedDeck);\n}\n\n// Flashcards API\nexport async function saveFlashcard(card: Flashcard): Promise<Flashcard> {\n  await flashcardsStore.setItem(card.id, card);\n  await updateDeckStats(card.deckId);\n  return card;\n}\n\n/**\n * Save a batch of flashcards and create/update a deck for them\n */\nexport async function saveFlashcards(\n  cards: Flashcard[],\n  documentId: string\n): Promise<Flashcard[]> {\n  if (!cards.length) return [];\n\n  // Create a deck for these flashcards if it doesn't exist\n  const deckId = crypto.randomUUID();\n  const document = await getCachedDocument(documentId);\n  const deckName = document\n    ? `Flashcards: ${document.name}`\n    : `Flashcards ${new Date().toLocaleDateString()}`;\n\n  const deck: FlashcardDeck = {\n    id: deckId,\n    name: deckName,\n    documentId,\n    description: document\n      ? `Generated from ${document.name}`\n      : \"Generated flashcards\",\n    createdAt: Date.now(),\n    totalCards: cards.length,\n    dueTodayCount: cards.length,\n    masteredCount: 0,\n  };\n\n  await saveDeck(deck);\n\n  // Save each flashcard with the deck ID\n  const savedCards: Flashcard[] = [];\n  for (const card of cards) {\n    const flashcard: Flashcard = {\n      ...card,\n      deckId,\n      createdAt: Date.now(),\n    };\n    await saveFlashcard(flashcard);\n    savedCards.push(flashcard);\n  }\n\n  return savedCards;\n}\n\nexport async function getFlashcard(id: string): Promise<Flashcard | null> {\n  return flashcardsStore.getItem<Flashcard>(id);\n}\n\nexport async function getFlashcardsByDeck(\n  deckId: string\n): Promise<Flashcard[]> {\n  const cards: Flashcard[] = [];\n  await flashcardsStore.iterate<Flashcard, void>((card) => {\n    if (card.deckId === deckId) {\n      cards.push(card);\n    }\n  });\n\n  return cards;\n}\n\nexport async function getDueFlashcards(deckId: string): Promise<Flashcard[]> {\n  const now = Date.now();\n  const cards = await getFlashcardsByDeck(deckId);\n  return cards.filter((card) => {\n    return !card.nextReview || card.nextReview <= now;\n  });\n}\n\nexport async function exportQuizzesAsJSON(): Promise<string> {\n  const response = await fetch('/api/quizzes/export?format=json', {\n    headers: {\n      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.error || 'Failed to export quizzes');\n  }\n\n  return response.text();\n}\n\nexport async function exportQuizzesAsCSV(): Promise<string> {\n  const response = await fetch('/api/quizzes/export?format=csv', {\n    headers: {\n      'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n    },\n  });\n\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}));\n    throw new Error(errorData.error || 'Failed to export quizzes');\n  }\n\n  return response.text();\n}\n\n// Document comparison functions\nexport async function deleteFlashcard(id: string): Promise<void> {\n  const card = await getFlashcard(id);\n  if (card) {\n    await flashcardsStore.removeItem(id);\n    await updateDeckStats(card.deckId);\n  }\n}\n\n// Study Statistics\nexport async function getStudyStats(): Promise<StudyStats> {\n  const documents = await getCachedDocuments();\n  const decks = await getAllDecks();\n\n  let totalFlashcards = 0;\n  let dueToday = 0;\n  let totalCorrect = 0;\n  let totalReviews = 0;\n\n  for (const deck of decks) {\n    totalFlashcards += deck.totalCards;\n    dueToday += deck.dueTodayCount;\n\n    const cards = await getFlashcardsByDeck(deck.id);\n    for (const card of cards) {\n      totalCorrect += card.correct || 0;\n      totalReviews += (card.correct || 0) + (card.incorrect || 0);\n    }\n  }\n\n  const accuracy = totalReviews > 0 ? (totalCorrect / totalReviews) * 100 : 0;\n\n  return {\n    totalDocuments: documents.length,\n    totalFlashcards,\n    dueToday,\n    accuracy,\n  };\n}\n\n// Export functionality\nexport async function exportFlashcardsAsJSON(): Promise<string> {\n  const decks = await getAllDecks();\n  const allData: {\n    decks: FlashcardDeck[];\n    flashcards: Record<string, Flashcard[]>;\n  } = {\n    decks: decks,\n    flashcards: {},\n  };\n\n  for (const deck of decks) {\n    const cards = await getFlashcardsByDeck(deck.id);\n    allData.flashcards[deck.id] = cards;\n  }\n\n  return JSON.stringify(allData, null, 2);\n}\n\nexport async function exportFlashcardsAsCSV(): Promise<string> {\n  const decks = await getAllDecks();\n  let csv =\n    \"Deck,Question,Answer,Created,Last Reviewed,Next Review,Interval,Easiness Factor\\n\";\n\n  for (const deck of decks) {\n    const cards = await getFlashcardsByDeck(deck.id);\n    for (const card of cards) {\n      const row = [\n        `\"${escapeCsvField(deck.name)}\"`,\n        `\"${escapeCsvField(card.question)}\"`,\n        `\"${escapeCsvField(card.answer)}\"`,\n        new Date(card.createdAt).toISOString(),\n        card.lastReviewed ? new Date(card.lastReviewed).toISOString() : \"\",\n        card.nextReview ? new Date(card.nextReview).toISOString() : \"\",\n        card.interval ? Math.round(card.interval / (24 * 60 * 60 * 1000)) : \"\",\n        card.easinessFactor || \"\",\n      ];\n      csv += row.join(\",\") + \"\\n\";\n    }\n  }\n\n  return csv;\n}\n\nfunction escapeCsvField(field: string): string {\n  return field.replace(/\"/g, '\"\"');\n}\n\n// Clear all data\nexport async function clearAllData(): Promise<void> {\n  await documentsStore.clear();\n  await decksStore.clear();\n  await flashcardsStore.clear();\n}\n"}