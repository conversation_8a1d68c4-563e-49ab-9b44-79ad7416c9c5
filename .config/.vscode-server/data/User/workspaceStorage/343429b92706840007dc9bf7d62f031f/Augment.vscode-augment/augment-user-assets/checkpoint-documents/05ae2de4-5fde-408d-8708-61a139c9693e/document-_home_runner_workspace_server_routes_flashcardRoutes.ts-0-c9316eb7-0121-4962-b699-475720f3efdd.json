{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/routes/flashcardRoutes.ts"}, "originalCode": "import { <PERSON>o } from \"hono\";\nimport type { Context, Next } from \"hono\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\n\n// Define the AppVariables type locally (same as in other route files)\nexport interface AppVariables {\n  supabase: any;\n  user: any;\n}\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nflashcardRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for flashcardRoutes\nflashcardRoutes.onError((err, c) => {\n  console.error(\"Error in flashcardRoutes:\", err);\n  return c.json(\n    {\n      error: \"An unexpected error occurred in flashcard routes.\",\n      message: err.message,\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`flashcardRoutes: Auth middleware triggered for path: ${c.req.path}`);\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\"flashcardRoutes: Auth Error - Authorization header missing or malformed.\");\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"flashcardRoutes: Auth Error - Token missing after Bearer split.\");\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  console.log(\"flashcardRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\");\n\n  try {\n    const supabase = c.get(\"supabase\");\n    if (!supabase) {\n      console.error(\"flashcardRoutes: Auth Error - Supabase client not available in context.\");\n      return c.json({ error: \"Internal server error: Supabase client not available\" }, 500);\n    }\n\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\"flashcardRoutes: Auth Error - getUser failed:\", getUserError.message);\n      if (getUserError.message.toLowerCase().includes(\"invalid token\") ||\n          getUserError.message.includes(\"jwt\")) {\n        return c.json({ error: \"Unauthorized: Invalid token\" }, 401);\n      }\n      return c.json({ error: \"Server error validating token\" }, 500);\n    }\n\n    const user = data?.user;\n    if (!user) {\n      console.error(\"flashcardRoutes: Auth Error - No user found for token.\");\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    if (!user.id) {\n      console.error(\"flashcardRoutes: Auth Error - User ID missing from authenticated user.\");\n      return c.json({ error: \"User ID missing from authenticated user\" }, 500);\n    }\n\n    console.log(`flashcardRoutes: Auth Success - User ${user.id} authenticated. Calling next().`);\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\"flashcardRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\", err.message, err.stack);\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nflashcardRoutes.use(\"*\", authMiddleware);\n\n// Route to update an individual flashcard\nflashcardRoutes.put(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    const body = await c.req.json();\n    const { front_text, back_text } = body;\n\n    if (!front_text || !back_text) {\n      return c.json({ error: \"Both front_text and back_text are required\" }, 400);\n    }\n\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Update the flashcard\n    const { data: updatedFlashcard, error: updateError } = await supabase\n      .from(\"flashcards\")\n      .update({\n        front_text: front_text.trim(),\n        back_text: back_text.trim(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error(\"Error updating flashcard:\", updateError);\n      return c.json(\n        { error: \"Failed to update flashcard\", details: updateError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard updated successfully\",\n      flashcard: updatedFlashcard,\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard update:\", error);\n    return c.json({ error: error.message || \"Failed to update flashcard\" }, 500);\n  }\n});\n\n// Route to delete an individual flashcard\nflashcardRoutes.delete(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Delete the flashcard\n    const { error: deleteError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard deleted successfully\",\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard deletion:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard\" }, 500);\n  }\n});\n\n// Route to get an individual flashcard\nflashcardRoutes.get(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // Get the flashcard if user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error fetching flashcard:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    return c.json(flashcard, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard\" }, 500);\n  }\n});\n\nexport default flashcardRoutes;\n", "modifiedCode": "import { <PERSON>o } from \"hono\";\nimport type { Context, Next } from \"hono\";\nimport { supabaseMiddleware } from \"../middleware/supabaseMiddleware\";\n\n// Define the AppVariables type locally (same as in other route files)\nexport interface AppVariables {\n  supabase: any;\n  user: any;\n}\n\nconst flashcardRoutes = new Hono<{ Variables: AppVariables }>();\n\n// Apply the Supabase middleware to all routes\nflashcardRoutes.use(\"*\", supabaseMiddleware);\n\n// Application-level error handler for flashcardRoutes\nflashcardRoutes.onError((err, c) => {\n  console.error(\"Error in flashcardRoutes:\", err);\n  return c.json(\n    {\n      error: \"An unexpected error occurred in flashcard routes.\",\n      message: err.message,\n    },\n    500\n  );\n});\n\n// Middleware to ensure user is authenticated\nconst authMiddleware = async (\n  c: Context<{ Variables: AppVariables }>,\n  next: Next\n) => {\n  console.log(`flashcardRoutes: Auth middleware triggered for path: ${c.req.path}`);\n  const authHeader = c.req.header(\"Authorization\");\n\n  if (!authHeader || !authHeader.startsWith(\"Bearer \")) {\n    console.error(\"flashcardRoutes: Auth Error - Authorization header missing or malformed.\");\n    return c.json({ error: \"Unauthorized: Missing or malformed token\" }, 401);\n  }\n\n  const token = authHeader.split(\" \")[1];\n  if (!token) {\n    console.error(\"flashcardRoutes: Auth Error - Token missing after Bearer split.\");\n    return c.json({ error: \"Unauthorized: Missing token\" }, 401);\n  }\n\n  console.log(\"flashcardRoutes: Auth - Token is present. Attempting supabase.auth.getUser(token).\");\n\n  try {\n    const supabase = c.get(\"supabase\");\n    if (!supabase) {\n      console.error(\"flashcardRoutes: Auth Error - Supabase client not available in context.\");\n      return c.json({ error: \"Internal server error: Supabase client not available\" }, 500);\n    }\n\n    const { data, error: getUserError } = await supabase.auth.getUser(token);\n\n    if (getUserError) {\n      console.error(\"flashcardRoutes: Auth Error - getUser failed:\", getUserError.message);\n      if (getUserError.message.toLowerCase().includes(\"invalid token\") ||\n          getUserError.message.includes(\"jwt\")) {\n        return c.json({ error: \"Unauthorized: Invalid token\" }, 401);\n      }\n      return c.json({ error: \"Server error validating token\" }, 500);\n    }\n\n    const user = data?.user;\n    if (!user) {\n      console.error(\"flashcardRoutes: Auth Error - No user found for token.\");\n      return c.json({ error: \"Unauthorized: No user found for token\" }, 401);\n    }\n\n    if (!user.id) {\n      console.error(\"flashcardRoutes: Auth Error - User ID missing from authenticated user.\");\n      return c.json({ error: \"User ID missing from authenticated user\" }, 500);\n    }\n\n    console.log(`flashcardRoutes: Auth Success - User ${user.id} authenticated. Calling next().`);\n    c.set(\"user\", user);\n    await next();\n  } catch (err: any) {\n    console.error(\"flashcardRoutes: Auth Fatal Error - Unexpected error in auth middleware try-catch block:\", err.message, err.stack);\n    return c.json(\n      { error: \"Internal server error during authentication processing\" },\n      500\n    );\n  }\n};\n\nflashcardRoutes.use(\"*\", authMiddleware);\n\n// Route to update an individual flashcard\nflashcardRoutes.put(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    const body = await c.req.json();\n    const { front_text, back_text } = body;\n\n    if (!front_text || !back_text) {\n      return c.json({ error: \"Both front_text and back_text are required\" }, 400);\n    }\n\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Update the flashcard\n    const { data: updatedFlashcard, error: updateError } = await supabase\n      .from(\"flashcards\")\n      .update({\n        front_text: front_text.trim(),\n        back_text: back_text.trim(),\n        updated_at: new Date().toISOString(),\n      })\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .select()\n      .single();\n\n    if (updateError) {\n      console.error(\"Error updating flashcard:\", updateError);\n      return c.json(\n        { error: \"Failed to update flashcard\", details: updateError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard updated successfully\",\n      flashcard: updatedFlashcard,\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard update:\", error);\n    return c.json({ error: error.message || \"Failed to update flashcard\" }, 500);\n  }\n});\n\n// Route to delete an individual flashcard\nflashcardRoutes.delete(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // First verify the flashcard exists and user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"id, user_id, set_id\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error checking flashcard ownership:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to verify flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    // Delete the flashcard\n    const { error: deleteError } = await supabase\n      .from(\"flashcards\")\n      .delete()\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id);\n\n    if (deleteError) {\n      console.error(\"Error deleting flashcard:\", deleteError);\n      return c.json(\n        { error: \"Failed to delete flashcard\", details: deleteError.message },\n        500\n      );\n    }\n\n    return c.json({\n      message: \"Flashcard deleted successfully\",\n    }, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard deletion:\", error);\n    return c.json({ error: error.message || \"Failed to delete flashcard\" }, 500);\n  }\n});\n\n// Route to get an individual flashcard\nflashcardRoutes.get(\"/:flashcardId\", async (c: Context<{ Variables: AppVariables }>) => {\n  const supabase = c.get(\"supabase\");\n  const user = c.get(\"user\");\n  const flashcardId = c.req.param(\"flashcardId\");\n\n  if (!flashcardId) {\n    return c.json({ error: \"Invalid flashcard ID\" }, 400);\n  }\n\n  try {\n    // Get the flashcard if user owns it\n    const { data: flashcard, error: flashcardError } = await supabase\n      .from(\"flashcards\")\n      .select(\"*\")\n      .eq(\"id\", flashcardId)\n      .eq(\"user_id\", user.id)\n      .single();\n\n    if (flashcardError) {\n      console.error(\"Error fetching flashcard:\", flashcardError);\n      if (flashcardError.code === \"PGRST116\") {\n        return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n      }\n      return c.json(\n        { error: \"Failed to fetch flashcard\", details: flashcardError.message },\n        500\n      );\n    }\n\n    if (!flashcard) {\n      return c.json({ error: \"Flashcard not found or you don't have access\" }, 404);\n    }\n\n    return c.json(flashcard, 200);\n  } catch (error: any) {\n    console.error(\"Error in flashcard fetch:\", error);\n    return c.json({ error: error.message || \"Failed to fetch flashcard\" }, 500);\n  }\n});\n\nexport default flashcardRoutes;\n"}