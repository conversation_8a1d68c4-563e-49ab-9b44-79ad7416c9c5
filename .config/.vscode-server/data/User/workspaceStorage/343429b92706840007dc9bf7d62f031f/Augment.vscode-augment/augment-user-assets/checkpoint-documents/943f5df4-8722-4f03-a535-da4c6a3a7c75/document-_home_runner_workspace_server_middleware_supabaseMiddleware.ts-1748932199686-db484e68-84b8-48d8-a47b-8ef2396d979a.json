{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/middleware/supabaseMiddleware.ts"}, "originalCode": "import { Context, Next } from 'hono';\nimport { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\n\n// Create Supabase clients outside the middleware to avoid creating new clients on each request\nconst supabaseUrl = supabaseConfig.url;\nconst supabaseServiceKey = supabaseConfig.serviceRoleKey;\nconst supabaseAnonKey = supabaseConfig.anonKey;\n\nconsole.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Anon Key:', supabaseAnonKey ? '✓ Found' : '✗ Not found');\n\n// Create two Supabase client instances:\n// 1. Service role client for admin operations (database queries, bypassing RLS)\nexport const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);\n\n// 2. Anonymous client for JWT token validation (user authentication)\nexport const supabaseAuthClient = createClient(supabaseUrl, supabaseAnonKey);\n\n// Middleware to attach the Supabase client to the Hono context\nexport const supabaseMiddleware = async (c: Context, next: Next) => {\n  if (!supabaseClient) {\n    console.error('Supabase client initialization failed');\n    return c.json({ \n      error: 'Internal Server Configuration Error: Supabase client initialization failed.' \n    }, 500);\n  }\n  \n  // Attach the Supabase client to the context\n  c.set('supabase', supabaseClient);\n  \n  // Log that the middleware is working (without sensitive headers)\n  console.log(`Supabase client attached to request context for ${c.req.method} ${c.req.url}`);\n\n  await next();\n};\n", "modifiedCode": "import { Context, Next } from 'hono';\nimport { createClient } from '@supabase/supabase-js';\nimport { supabaseConfig } from '../config';\n\n// Create Supabase clients outside the middleware to avoid creating new clients on each request\nconst supabaseUrl = supabaseConfig.url;\nconst supabaseServiceKey = supabaseConfig.serviceRoleKey;\nconst supabaseAnonKey = supabaseConfig.anonKey;\n\nconsole.log('Using Supabase URL:', supabaseUrl ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Service Key:', supabaseServiceKey ? '✓ Found' : '✗ Not found');\nconsole.log('Using Supabase Anon Key:', supabaseAnonKey ? '✓ Found' : '✗ Not found');\n\n// Create two Supabase client instances:\n// 1. Service role client for admin operations (database queries, bypassing RLS)\nexport const supabaseClient = createClient(supabaseUrl, supabaseServiceKey);\n\n// 2. Anonymous client for JWT token validation (user authentication)\nexport const supabaseAuthClient = createClient(supabaseUrl, supabaseAnonKey);\n\n// Middleware to attach both Supabase clients to the Hono context\nexport const supabaseMiddleware = async (c: Context, next: Next) => {\n  if (!supabaseClient || !supabaseAuthClient) {\n    console.error('Supabase client initialization failed');\n    return c.json({\n      error: 'Internal Server Configuration Error: Supabase client initialization failed.'\n    }, 500);\n  }\n\n  // Attach both Supabase clients to the context\n  c.set('supabase', supabaseClient); // For database operations\n  c.set('supabaseAuth', supabaseAuthClient); // For JWT token validation\n\n  // Log that the middleware is working (without sensitive headers)\n  console.log(`Supabase clients attached to request context for ${c.req.method} ${c.req.url}`);\n\n  await next();\n};\n"}