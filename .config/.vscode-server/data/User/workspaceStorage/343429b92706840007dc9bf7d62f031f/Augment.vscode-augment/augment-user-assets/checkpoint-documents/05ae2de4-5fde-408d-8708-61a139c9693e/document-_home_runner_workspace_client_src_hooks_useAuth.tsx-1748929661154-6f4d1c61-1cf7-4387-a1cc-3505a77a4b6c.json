{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "client/src/hooks/useAuth.tsx"}, "originalCode": "import React, {\n  createContext,\n  useContext,\n  useEffect,\n  useState,\n  ReactNode,\n} from \"react\";\nimport { Session, User } from \"@supabase/supabase-js\";\n\ninterface AuthContextType {\n  session: Session | null;\n  user: User | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  signUp: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n}\n\n// Export AuthContext for direct checking if needed elsewhere\nexport const AuthContext = createContext<AuthContextType | undefined>(\n  undefined\n);\n\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\n  const [session, setSession] = useState<Session | null>(null);\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkSession = async () => {\n      const token = localStorage.getItem('auth_token');\n      const refreshToken = localStorage.getItem('refresh_token');\n\n      if (token) {\n        try {\n          const response = await fetch('/api/auth/session', {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n            },\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            setUser(data.user);\n            // Create a minimal session object\n            setSession({\n              access_token: token,\n              refresh_token: refreshToken || '',\n              expires_in: 3600,\n              token_type: 'bearer',\n              user: data.user,\n            } as Session);\n          } else if (response.status === 401) {\n            // Token is invalid/expired, try to refresh if we have a refresh token\n            if (refreshToken) {\n              try {\n                const refreshResponse = await fetch('/api/auth/refresh', {\n                  method: 'POST',\n                  headers: {\n                    'Content-Type': 'application/json',\n                  },\n                  body: JSON.stringify({ refresh_token: refreshToken }),\n                });\n\n                if (refreshResponse.ok) {\n                  const refreshData = await refreshResponse.json();\n                  setUser(refreshData.user);\n                  setSession(refreshData.session);\n                  localStorage.setItem('auth_token', refreshData.session.access_token);\n                  localStorage.setItem('refresh_token', refreshData.session.refresh_token);\n                } else {\n                  // Refresh failed, clear tokens\n                  localStorage.removeItem('auth_token');\n                  localStorage.removeItem('refresh_token');\n                }\n              } catch (refreshError) {\n                console.error('Token refresh failed:', refreshError);\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('refresh_token');\n              }\n            } else {\n              // No refresh token, clear everything\n              localStorage.removeItem('auth_token');\n              localStorage.removeItem('refresh_token');\n            }\n          } else {\n            // Other error, clear tokens\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('refresh_token');\n          }\n        } catch (error) {\n          console.error('Session check failed:', error);\n          localStorage.removeItem('auth_token');\n          localStorage.removeItem('refresh_token');\n        }\n      }\n\n      setLoading(false);\n    };\n\n    checkSession();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        setSession(data.session);\n        setUser(data.user);\n        localStorage.setItem('auth_token', data.session.access_token);\n        localStorage.setItem('refresh_token', data.session.refresh_token);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Login failed' };\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'Network error' };\n    }\n  };\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/signup', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        if (data.session && data.user) {\n          setSession(data.session);\n          setUser(data.user);\n          localStorage.setItem('auth_token', data.session.access_token);\n          localStorage.setItem('refresh_token', data.session.refresh_token);\n        }\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Signup failed' };\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'Network error' };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        await fetch('/api/auth/logout', {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setSession(null);\n      setUser(null);\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('refresh_token');\n    }\n  };\n\n  const value = {\n    session,\n    user,\n    loading,\n    signOut,\n    signIn,\n    signUp,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n", "modifiedCode": "import React, {\n  createContext,\n  useContext,\n  useEffect,\n  useState,\n  ReactNode,\n} from \"react\";\nimport { Session, User } from \"@supabase/supabase-js\";\n\ninterface AuthContextType {\n  session: Session | null;\n  user: User | null;\n  loading: boolean;\n  signOut: () => Promise<void>;\n  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n  signUp: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;\n}\n\n// Export AuthContext for direct checking if needed elsewhere\nexport const AuthContext = createContext<AuthContextType | undefined>(\n  undefined\n);\n\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\n  const [session, setSession] = useState<Session | null>(null);\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check for existing session on mount\n  useEffect(() => {\n    const checkSession = async () => {\n      const token = localStorage.getItem('auth_token');\n      const refreshToken = localStorage.getItem('refresh_token');\n\n      if (token && token.trim() !== '') {\n        try {\n          const response = await fetch('/api/auth/session', {\n            headers: {\n              'Authorization': `Bearer ${token}`,\n            },\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            setUser(data.user);\n            // Create a minimal session object\n            setSession({\n              access_token: token,\n              refresh_token: refreshToken || '',\n              expires_in: 3600,\n              token_type: 'bearer',\n              user: data.user,\n            } as Session);\n          } else if (response.status === 401) {\n            // Token is invalid/expired, try to refresh if we have a refresh token\n            if (refreshToken) {\n              try {\n                const refreshResponse = await fetch('/api/auth/refresh', {\n                  method: 'POST',\n                  headers: {\n                    'Content-Type': 'application/json',\n                  },\n                  body: JSON.stringify({ refresh_token: refreshToken }),\n                });\n\n                if (refreshResponse.ok) {\n                  const refreshData = await refreshResponse.json();\n                  setUser(refreshData.user);\n                  setSession(refreshData.session);\n                  localStorage.setItem('auth_token', refreshData.session.access_token);\n                  localStorage.setItem('refresh_token', refreshData.session.refresh_token);\n                } else {\n                  // Refresh failed, clear tokens\n                  localStorage.removeItem('auth_token');\n                  localStorage.removeItem('refresh_token');\n                }\n              } catch (refreshError) {\n                console.error('Token refresh failed:', refreshError);\n                localStorage.removeItem('auth_token');\n                localStorage.removeItem('refresh_token');\n              }\n            } else {\n              // No refresh token, clear everything\n              localStorage.removeItem('auth_token');\n              localStorage.removeItem('refresh_token');\n            }\n          } else {\n            // Other error, clear tokens\n            localStorage.removeItem('auth_token');\n            localStorage.removeItem('refresh_token');\n          }\n        } catch (error) {\n          console.error('Session check failed:', error);\n          localStorage.removeItem('auth_token');\n          localStorage.removeItem('refresh_token');\n        }\n      }\n\n      setLoading(false);\n    };\n\n    checkSession();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        setSession(data.session);\n        setUser(data.user);\n        localStorage.setItem('auth_token', data.session.access_token);\n        localStorage.setItem('refresh_token', data.session.refresh_token);\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Login failed' };\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'Network error' };\n    }\n  };\n\n  const signUp = async (email: string, password: string) => {\n    try {\n      const response = await fetch('/api/auth/signup', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (response.ok && data.success) {\n        if (data.session && data.user) {\n          setSession(data.session);\n          setUser(data.user);\n          localStorage.setItem('auth_token', data.session.access_token);\n          localStorage.setItem('refresh_token', data.session.refresh_token);\n        }\n        return { success: true };\n      } else {\n        return { success: false, error: data.error || 'Signup failed' };\n      }\n    } catch (error: any) {\n      return { success: false, error: error.message || 'Network error' };\n    }\n  };\n\n  const signOut = async () => {\n    try {\n      const token = localStorage.getItem('auth_token');\n      if (token) {\n        await fetch('/api/auth/logout', {\n          method: 'POST',\n          headers: {\n            'Authorization': `Bearer ${token}`,\n          },\n        });\n      }\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setSession(null);\n      setUser(null);\n      localStorage.removeItem('auth_token');\n      localStorage.removeItem('refresh_token');\n    }\n  };\n\n  const value = {\n    session,\n    user,\n    loading,\n    signOut,\n    signIn,\n    signUp,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error(\"useAuth must be used within an AuthProvider\");\n  }\n  return context;\n};\n"}