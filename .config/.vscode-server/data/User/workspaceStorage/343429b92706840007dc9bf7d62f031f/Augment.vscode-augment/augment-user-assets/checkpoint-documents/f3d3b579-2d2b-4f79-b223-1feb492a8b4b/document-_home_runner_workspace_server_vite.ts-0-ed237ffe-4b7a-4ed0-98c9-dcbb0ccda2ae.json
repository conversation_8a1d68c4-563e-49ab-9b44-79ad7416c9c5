{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/vite.ts"}, "originalCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer } from \"vite\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nexport function log(message: string) {\n  const now = new Date();\n  const timeStr = now.toLocaleTimeString();\n  console.log(`${timeStr} [express] ${message}`);\n}\n\nexport async function setupVite(app: Express) {\n  if (process.env.NODE_ENV === \"production\") {\n    log(\"Production mode detected, skipping Vite setup\");\n    return;\n  }\n\n  log(\"Setting up Vite dev middleware...\");\n  \n  try {\n    const vite = await createViteServer({\n      server: { middlewareMode: true },\n      appType: \"spa\",\n      // Pass any additional Vite config options\n    });\n\n    app.use(vite.middlewares);\n    log(\"Vite dev middleware set up successfully\");\n  } catch (e: any) {\n    log(`Error setting up Vite: ${e.message}`);\n    if (e.stack) log(e.stack);\n    process.exit(1);\n  }\n}\n\nexport function serveStatic(app: Express) {\n  // In production, serve the built frontend from the dist/public directory\n  const distPath = path.resolve(process.cwd(), \"dist/public\");\n\n  // Check if the dist/public directory exists\n  if (!fs.existsSync(distPath)) {\n    log(`Warning: Static files directory ${distPath} does not exist!`);\n    log('Make sure to build the client first with npm run build');\n    return;\n  }\n\n  log(`Serving static files from ${distPath}`);\n\n  // Security headers middleware for static files\n  app.use((req, res, next) => {\n    // Skip API routes - they have their own security headers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n\n    // Security headers for static content\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n    res.setHeader('X-Frame-Options', 'DENY');\n    res.setHeader('X-XSS-Protection', '1; mode=block');\n    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');\n\n    // Content Security Policy for production\n    if (process.env.NODE_ENV === 'production') {\n      res.setHeader('Content-Security-Policy',\n        \"default-src 'self'; \" +\n        \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; \" +\n        \"style-src 'self' 'unsafe-inline'; \" +\n        \"img-src 'self' data: https:; \" +\n        \"font-src 'self' data:; \" +\n        \"connect-src 'self' https://hrdjfukhzbzksqaupqie.supabase.co https://openrouter.ai wss:; \" +\n        \"frame-src 'none';\"\n      );\n    }\n\n    next();\n  });\n\n  // Serve static files with optimized caching\n  app.use(express.static(distPath, {\n    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,\n    etag: true,\n    index: false, // Don't automatically serve index.html, we'll handle that below\n    setHeaders: (res, path) => {\n      // Set specific cache headers for different file types\n      if (path.endsWith('.html')) {\n        res.setHeader('Cache-Control', 'no-cache');\n      } else if (path.match(/\\.(js|css|woff2?|ttf|eot)$/)) {\n        res.setHeader('Cache-Control', 'public, max-age=********, immutable');\n      }\n    }\n  }));\n\n  // For SPA routing - all non-API routes should serve the index.html\n  app.get(\"*\", (req, res, next) => {\n    // Skip API routes and let them be handled by their respective handlers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n\n    // Serve the index.html for client-side routing\n    const indexPath = path.join(distPath, \"index.html\");\n    if (fs.existsSync(indexPath)) {\n      // Set no-cache for index.html to ensure updates are picked up\n      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');\n      res.setHeader('Pragma', 'no-cache');\n      res.setHeader('Expires', '0');\n      res.sendFile(indexPath);\n    } else {\n      log(`Warning: index.html not found at ${indexPath}`);\n      res.status(404).send('Frontend build not found');\n    }\n  });\n}\n", "modifiedCode": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer } from \"vite\";\nimport { fileURLToPath } from \"url\";\n\n// Get current file's directory in ES modules\nconst __filename = fileURLToPath(import.meta.url);\nconst __dirname = path.dirname(__filename);\n\nexport function log(message: string) {\n  const now = new Date();\n  const timeStr = now.toLocaleTimeString();\n  console.log(`${timeStr} [express] ${message}`);\n}\n\nexport async function setupVite(app: Express) {\n  if (process.env.NODE_ENV === \"production\") {\n    log(\"Production mode detected, skipping Vite setup\");\n    return;\n  }\n\n  log(\"Setting up Vite dev middleware...\");\n  \n  try {\n    const vite = await createViteServer({\n      server: { middlewareMode: true },\n      appType: \"spa\",\n      // Pass any additional Vite config options\n    });\n\n    app.use(vite.middlewares);\n    log(\"Vite dev middleware set up successfully\");\n  } catch (e: any) {\n    log(`Error setting up Vite: ${e.message}`);\n    if (e.stack) log(e.stack);\n    process.exit(1);\n  }\n}\n\nexport function serveStatic(app: Express) {\n  // In production, serve the built frontend from the dist/public directory\n  const distPath = path.resolve(process.cwd(), \"dist/public\");\n\n  // Check if the dist/public directory exists\n  if (!fs.existsSync(distPath)) {\n    log(`Warning: Static files directory ${distPath} does not exist!`);\n    log('Make sure to build the client first with npm run build');\n    return;\n  }\n\n  log(`Serving static files from ${distPath}`);\n\n  // Security headers middleware for static files\n  app.use((req, res, next) => {\n    // Skip API routes - they have their own security headers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n\n    // Security headers for static content\n    res.setHeader('X-Content-Type-Options', 'nosniff');\n    res.setHeader('X-Frame-Options', 'DENY');\n    res.setHeader('X-XSS-Protection', '1; mode=block');\n    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');\n\n    // Content Security Policy for production\n    if (process.env.NODE_ENV === 'production') {\n      res.setHeader('Content-Security-Policy',\n        \"default-src 'self'; \" +\n        \"script-src 'self' 'unsafe-inline' 'unsafe-eval' https://replit.com; \" +\n        \"style-src 'self' 'unsafe-inline'; \" +\n        \"img-src 'self' data: https:; \" +\n        \"font-src 'self' data:; \" +\n        \"connect-src 'self' https://hrdjfukhzbzksqaupqie.supabase.co https://openrouter.ai wss:; \" +\n        \"frame-src 'none';\"\n      );\n    }\n\n    next();\n  });\n\n  // Serve static files with optimized caching\n  app.use(express.static(distPath, {\n    maxAge: process.env.NODE_ENV === 'production' ? '1y' : 0,\n    etag: true,\n    index: false, // Don't automatically serve index.html, we'll handle that below\n    setHeaders: (res, path) => {\n      // Set specific cache headers for different file types\n      if (path.endsWith('.html')) {\n        res.setHeader('Cache-Control', 'no-cache');\n      } else if (path.match(/\\.(js|css|woff2?|ttf|eot)$/)) {\n        res.setHeader('Cache-Control', 'public, max-age=********, immutable');\n      }\n    }\n  }));\n\n  // For SPA routing - all non-API routes should serve the index.html\n  app.get(\"*\", (req, res, next) => {\n    // Skip API routes and let them be handled by their respective handlers\n    if (req.path.startsWith(\"/api\")) {\n      return next();\n    }\n\n    // Serve the index.html for client-side routing\n    const indexPath = path.join(distPath, \"index.html\");\n    if (fs.existsSync(indexPath)) {\n      // Set no-cache for index.html to ensure updates are picked up\n      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');\n      res.setHeader('Pragma', 'no-cache');\n      res.setHeader('Expires', '0');\n      res.sendFile(indexPath);\n    } else {\n      log(`Warning: index.html not found at ${indexPath}`);\n      res.status(404).send('Frontend build not found');\n    }\n  });\n}\n"}