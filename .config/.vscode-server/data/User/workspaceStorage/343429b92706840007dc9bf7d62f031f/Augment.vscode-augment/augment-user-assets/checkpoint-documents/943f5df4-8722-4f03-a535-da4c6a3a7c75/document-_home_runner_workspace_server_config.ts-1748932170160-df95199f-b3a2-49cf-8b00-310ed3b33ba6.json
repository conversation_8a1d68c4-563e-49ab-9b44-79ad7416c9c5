{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "server/config.ts"}, "originalCode": "// Production-ready configuration using environment variables\n// Fallback values should only be used for development\n\nexport const supabaseConfig = {\n  url: process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || \"\",\n  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || \"\",\n  anonKey: process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || \"\",\n  dbPassword: process.env.VITE_DATABASE_PASSWORD || \"\",\n};\n\n// Validate required environment variables in production\nif (process.env.NODE_ENV === 'production') {\n  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];\n  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);\n\n  if (missingVars.length > 0) {\n    console.error('❌ Missing required environment variables for production:', missingVars);\n    console.error('Please set these environment variables before starting the server.');\n    process.exit(1);\n  }\n}\n\n// Validation - only check essential config\nif (!supabaseConfig.url || !supabaseConfig.serviceRoleKey || !supabaseConfig.anonKey) {\n  console.error(\n    \"❌ Error: Supabase URL, Service Role Key, and Anonymous Key must be configured.\"\n  );\n  console.error(\"Current config:\", {\n    url: supabaseConfig.url ? \"✓ Set\" : \"✗ Missing\",\n    serviceRoleKey: supabaseConfig.serviceRoleKey ? \"✓ Set\" : \"✗ Missing\",\n    anonKey: supabaseConfig.anonKey ? \"✓ Set\" : \"✗ Missing\",\n  });\n  process.exit(1);\n}\n\n// Log configuration status\nconsole.log(\"✓ Supabase configuration loaded successfully\");\nconsole.log(\"✓ URL:\", supabaseConfig.url ? \"Configured\" : \"Missing\");\nconsole.log(\n  \"✓ Service Role Key:\",\n  supabaseConfig.serviceRoleKey ? \"Configured\" : \"Missing\"\n);\nconsole.log(\n  \"✓ Database Password:\",\n  supabaseConfig.dbPassword\n    ? \"Configured\"\n    : \"Optional - using service role key authentication\"\n);\n", "modifiedCode": "// Production-ready configuration using environment variables\n// Fallback values should only be used for development\n\nexport const supabaseConfig = {\n  url: process.env.SUPABASE_URL || process.env.VITE_SUPABASE_URL || \"\",\n  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY || \"\",\n  anonKey: process.env.SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY || \"\",\n  dbPassword: process.env.VITE_DATABASE_PASSWORD || \"\",\n};\n\n// Validate required environment variables in production\nif (process.env.NODE_ENV === 'production') {\n  const requiredEnvVars = ['SUPABASE_URL', 'SUPABASE_SERVICE_ROLE_KEY'];\n  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);\n\n  if (missingVars.length > 0) {\n    console.error('❌ Missing required environment variables for production:', missingVars);\n    console.error('Please set these environment variables before starting the server.');\n    process.exit(1);\n  }\n}\n\n// Validation - only check essential config\nif (!supabaseConfig.url || !supabaseConfig.serviceRoleKey || !supabaseConfig.anonKey) {\n  console.error(\n    \"❌ Error: Supabase URL, Service Role Key, and Anonymous Key must be configured.\"\n  );\n  console.error(\"Current config:\", {\n    url: supabaseConfig.url ? \"✓ Set\" : \"✗ Missing\",\n    serviceRoleKey: supabaseConfig.serviceRoleKey ? \"✓ Set\" : \"✗ Missing\",\n    anonKey: supabaseConfig.anonKey ? \"✓ Set\" : \"✗ Missing\",\n  });\n  process.exit(1);\n}\n\n// Log configuration status\nconsole.log(\"✓ Supabase configuration loaded successfully\");\nconsole.log(\"✓ URL:\", supabaseConfig.url ? \"Configured\" : \"Missing\");\nconsole.log(\n  \"✓ Service Role Key:\",\n  supabaseConfig.serviceRoleKey ? \"Configured\" : \"Missing\"\n);\nconsole.log(\n  \"✓ Anonymous Key:\",\n  supabaseConfig.anonKey ? \"Configured\" : \"Missing\"\n);\nconsole.log(\n  \"✓ Database Password:\",\n  supabaseConfig.dbPassword\n    ? \"Configured\"\n    : \"Optional - using service role key authentication\"\n);\n"}