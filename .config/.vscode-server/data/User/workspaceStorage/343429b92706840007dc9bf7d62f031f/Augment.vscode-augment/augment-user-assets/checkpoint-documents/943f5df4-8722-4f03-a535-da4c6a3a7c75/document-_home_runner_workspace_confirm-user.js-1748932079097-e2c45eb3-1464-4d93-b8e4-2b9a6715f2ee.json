{"path": {"rootPath": "/home/<USER>/workspace", "relPath": "confirm-user.js"}, "modifiedCode": "#!/usr/bin/env node\n\n// <PERSON><PERSON><PERSON> to manually confirm a user for testing\nimport { createClient } from '@supabase/supabase-js';\n\nconst SUPABASE_URL = process.env.SUPABASE_URL || 'https://hrdjfukhzbzksqaupqie.supabase.co';\nconst SUPABASE_SERVICE_ROLE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhyZGpmdWtoemJ6a3NxYXVwcWllIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NzI0Mjk0OSwiZXhwIjoyMDYyODE4OTQ5fQ.bv1RuHjvDkd3u3qYDHfP2D7PZLFPQreKo2j1s9oXUA0';\n\nconst supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);\n\nasync function confirmUser(email) {\n  try {\n    console.log(`Attempting to confirm user: ${email}`);\n    \n    // First, get the user by email\n    const { data: users, error: getUserError } = await supabase.auth.admin.listUsers();\n    \n    if (getUserError) {\n      console.error('Error listing users:', getUserError);\n      return;\n    }\n    \n    const user = users.users.find(u => u.email === email);\n    if (!user) {\n      console.error('User not found:', email);\n      return;\n    }\n    \n    console.log('Found user:', user.id, user.email, 'confirmed:', user.email_confirmed_at);\n    \n    // Confirm the user\n    const { data, error } = await supabase.auth.admin.updateUserById(user.id, {\n      email_confirm: true\n    });\n    \n    if (error) {\n      console.error('Error confirming user:', error);\n      return;\n    }\n    \n    console.log('User confirmed successfully:', data.user.email);\n    \n  } catch (error) {\n    console.error('Unexpected error:', error);\n  }\n}\n\n// Get email from command line argument\nconst email = process.argv[2];\nif (!email) {\n  console.error('Usage: node confirm-user.js <email>');\n  process.exit(1);\n}\n\nconfirmUser(email);\n"}