2025-06-03 05:40:03.941 [info] [main] Log level: Info
2025-06-03 05:40:03.941 [info] [main] Validating found git in: "git"
2025-06-03 05:40:03.941 [info] [main] Using git "2.47.2" from "git"
2025-06-03 05:40:03.941 [info] [Model][doInitialScan] Initial repository scan started
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --git-dir --git-common-dir [1ms]
2025-06-03 05:40:03.941 [info] [Model][openRepository] Opened repository (path): /home/<USER>/workspace
2025-06-03 05:40:03.941 [info] [Model][openRepository] Opened repository (real path): /home/<USER>/workspace
2025-06-03 05:40:03.941 [info] > git config --get commit.template [2ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [467ms]
2025-06-03 05:40:03.941 [info] > git status -z -uall [55ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [12ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [55ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [40ms]
2025-06-03 05:40:03.941 [info] > git config --get --local branch.main.vscode-merge-base [24ms]
2025-06-03 05:40:03.941 [info] > git config --get commit.template [57ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [66ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/origin/main refs/remotes/origin/main [11ms]
2025-06-03 05:40:03.941 [info] > git merge-base refs/heads/main refs/remotes/origin/main [3ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [14ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [19ms]
2025-06-03 05:40:03.941 [info] > git diff --name-status -z --diff-filter=ADMR 435a5ff9421201dec9d4e684da4764850c24096a...refs/remotes/origin/main [6ms]
2025-06-03 05:40:03.941 [info] > git status -z -uall [8ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [18ms]
2025-06-03 05:40:03.941 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [11ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [9ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [11ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.941 [info] > git rev-parse --show-toplevel [8ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [46ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [5ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [1ms]
2025-06-03 05:40:03.942 [info] > git rev-parse --show-toplevel [2ms]
2025-06-03 05:40:03.942 [info] [Model][doInitialScan] Initial repository scan completed - repositories (1), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-06-03 05:40:04.538 [info] > git config --get commit.template [24ms]
2025-06-03 05:40:04.567 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [16ms]
2025-06-03 05:40:05.327 [info] > git status -z -uall [22ms]
2025-06-03 05:40:05.334 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [21ms]
2025-06-03 05:40:08.225 [info] > git config --get --local branch.main.github-pr-owner-number [182ms]
2025-06-03 05:40:08.225 [warning] [Git][config] git config failed: Failed to execute git
2025-06-03 05:40:11.860 [info] > git config --get commit.template [42ms]
2025-06-03 05:40:11.861 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [12ms]
2025-06-03 05:40:11.901 [info] > git status -z -uall [10ms]
2025-06-03 05:40:11.902 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 05:40:12.089 [info] > git fetch [289ms]
2025-06-03 05:40:12.089 [info] Missing or invalid credentials.
Skip silent fetch commands
Missing or invalid credentials.
Skip silent fetch commands
remote: Repository not found.
fatal: Authentication failed for 'https://github.com/Chewy42/ChewyAI/'
2025-06-03 05:40:12.100 [info] > git config --get commit.template [2ms]
2025-06-03 05:40:12.114 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [4ms]
2025-06-03 05:40:12.142 [info] > git status -z -uall [10ms]
2025-06-03 05:40:12.143 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:41:17.837 [info] > git show --textconv :.replit [11ms]
2025-06-03 05:41:17.839 [info] > git ls-files --stage -- .replit [4ms]
2025-06-03 05:41:17.852 [info] > git cat-file -s 94429ce8e752f0414fc449966fbe9040ae43a887 [1ms]
2025-06-03 05:41:17.987 [info] > git check-ignore -v -z --stdin [2ms]
2025-06-03 05:41:21.296 [info] > git blame --root --incremental 435a5ff9421201dec9d4e684da4764850c24096a -- .replit [3ms]
2025-06-03 05:45:07.095 [info] > git ls-files --stage -- .replit [2ms]
2025-06-03 05:45:07.105 [info] > git cat-file -s 94429ce8e752f0414fc449966fbe9040ae43a887 [1ms]
2025-06-03 05:45:07.183 [info] > git show --textconv :.replit [1ms]
2025-06-03 05:45:07.538 [info] > git config --get commit.template [7ms]
2025-06-03 05:45:07.538 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:45:07.553 [info] > git status -z -uall [7ms]
2025-06-03 05:45:07.554 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:14.528 [info] > git config --get commit.template [2ms]
2025-06-03 05:45:14.543 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 05:45:14.560 [info] > git status -z -uall [6ms]
2025-06-03 05:45:14.561 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:19.582 [info] > git config --get commit.template [8ms]
2025-06-03 05:45:19.583 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:45:19.594 [info] > git status -z -uall [5ms]
2025-06-03 05:45:19.595 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 05:45:24.617 [info] > git config --get commit.template [6ms]
2025-06-03 05:45:24.618 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:45:24.632 [info] > git status -z -uall [7ms]
2025-06-03 05:45:24.633 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 05:45:35.330 [info] > git config --get commit.template [6ms]
2025-06-03 05:45:35.331 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:45:35.343 [info] > git status -z -uall [6ms]
2025-06-03 05:45:35.344 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:40.366 [info] > git config --get commit.template [7ms]
2025-06-03 05:45:40.367 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:45:40.381 [info] > git status -z -uall [8ms]
2025-06-03 05:45:40.382 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:45:45.402 [info] > git config --get commit.template [7ms]
2025-06-03 05:45:45.403 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:45:45.417 [info] > git status -z -uall [6ms]
2025-06-03 05:45:45.418 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:47:04.215 [info] > git config --get commit.template [13ms]
2025-06-03 05:47:04.216 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:47:04.229 [info] > git status -z -uall [6ms]
2025-06-03 05:47:04.230 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:47:09.253 [info] > git config --get commit.template [1ms]
2025-06-03 05:47:09.265 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:47:09.283 [info] > git status -z -uall [7ms]
2025-06-03 05:47:09.284 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:47:14.330 [info] > git config --get commit.template [12ms]
2025-06-03 05:47:14.330 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:47:14.368 [info] > git status -z -uall [16ms]
2025-06-03 05:47:14.369 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 05:47:45.074 [info] > git config --get commit.template [10ms]
2025-06-03 05:47:45.075 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:47:45.095 [info] > git status -z -uall [7ms]
2025-06-03 05:47:45.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 05:48:16.447 [info] > git config --get commit.template [11ms]
2025-06-03 05:48:16.448 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:48:16.470 [info] > git status -z -uall [11ms]
2025-06-03 05:48:16.471 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:48:21.492 [info] > git config --get commit.template [7ms]
2025-06-03 05:48:21.493 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:48:21.506 [info] > git status -z -uall [7ms]
2025-06-03 05:48:21.507 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:51:12.485 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:51:12.485 [info] > git config --get commit.template [9ms]
2025-06-03 05:51:12.501 [info] > git status -z -uall [7ms]
2025-06-03 05:51:12.502 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:51:17.526 [info] > git config --get commit.template [8ms]
2025-06-03 05:51:17.527 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 05:51:17.543 [info] > git status -z -uall [9ms]
2025-06-03 05:51:17.543 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 05:59:17.622 [info] > git config --get commit.template [7ms]
2025-06-03 05:59:17.622 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 05:59:17.636 [info] > git status -z -uall [6ms]
2025-06-03 05:59:17.638 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:01:23.941 [info] > git config --get commit.template [8ms]
2025-06-03 06:01:23.966 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [25ms]
2025-06-03 06:01:23.997 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [6ms]
2025-06-03 06:01:23.999 [info] > git status -z -uall [19ms]
2025-06-03 06:01:29.025 [info] > git config --get commit.template [10ms]
2025-06-03 06:01:29.026 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:01:29.047 [info] > git status -z -uall [10ms]
2025-06-03 06:01:29.049 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:01:34.073 [info] > git config --get commit.template [8ms]
2025-06-03 06:01:34.074 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:01:34.095 [info] > git status -z -uall [7ms]
2025-06-03 06:01:34.096 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:01:51.887 [info] > git config --get commit.template [14ms]
2025-06-03 06:01:51.888 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:01:51.920 [info] > git status -z -uall [17ms]
2025-06-03 06:01:51.921 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 06:26:16.539 [info] > git config --get commit.template [2ms]
2025-06-03 06:26:16.554 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:26:16.589 [info] > git status -z -uall [10ms]
2025-06-03 06:26:16.590 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:26:21.618 [info] > git config --get commit.template [9ms]
2025-06-03 06:26:21.619 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:26:21.636 [info] > git status -z -uall [8ms]
2025-06-03 06:26:21.637 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 06:28:28.053 [info] > git config --get commit.template [9ms]
2025-06-03 06:28:28.054 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:28:28.076 [info] > git status -z -uall [10ms]
2025-06-03 06:28:28.077 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:28:33.104 [info] > git config --get commit.template [8ms]
2025-06-03 06:28:33.105 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:28:33.123 [info] > git status -z -uall [9ms]
2025-06-03 06:28:33.124 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:54:22.855 [info] > git config --get commit.template [2ms]
2025-06-03 06:54:22.865 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:54:22.884 [info] > git status -z -uall [8ms]
2025-06-03 06:54:22.885 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:54:27.916 [info] > git config --get commit.template [13ms]
2025-06-03 06:54:27.917 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:54:27.943 [info] > git status -z -uall [13ms]
2025-06-03 06:54:27.944 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 06:54:39.360 [info] > git config --get commit.template [11ms]
2025-06-03 06:54:39.361 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:54:39.386 [info] > git status -z -uall [11ms]
2025-06-03 06:54:39.386 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 06:54:44.413 [info] > git config --get commit.template [2ms]
2025-06-03 06:54:44.434 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [3ms]
2025-06-03 06:54:44.478 [info] > git status -z -uall [17ms]
2025-06-03 06:54:44.480 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:54:49.511 [info] > git config --get commit.template [13ms]
2025-06-03 06:54:49.512 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:54:49.534 [info] > git status -z -uall [9ms]
2025-06-03 06:54:49.535 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:54:54.588 [info] > git config --get commit.template [18ms]
2025-06-03 06:54:54.589 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:54:54.630 [info] > git status -z -uall [18ms]
2025-06-03 06:54:54.631 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:54:59.659 [info] > git config --get commit.template [1ms]
2025-06-03 06:54:59.679 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:54:59.721 [info] > git status -z -uall [27ms]
2025-06-03 06:54:59.723 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 06:58:04.757 [info] > git config --get commit.template [12ms]
2025-06-03 06:58:04.784 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [27ms]
2025-06-03 06:58:04.815 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [7ms]
2025-06-03 06:58:04.842 [info] > git status -z -uall [43ms]
2025-06-03 06:58:09.886 [info] > git config --get commit.template [20ms]
2025-06-03 06:58:09.887 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:58:09.912 [info] > git status -z -uall [11ms]
2025-06-03 06:58:09.912 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:58:14.942 [info] > git config --get commit.template [11ms]
2025-06-03 06:58:14.943 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:58:14.972 [info] > git status -z -uall [15ms]
2025-06-03 06:58:14.973 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 06:58:20.006 [info] > git config --get commit.template [15ms]
2025-06-03 06:58:20.007 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:58:20.037 [info] > git status -z -uall [18ms]
2025-06-03 06:58:20.039 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:58:25.065 [info] > git config --get commit.template [9ms]
2025-06-03 06:58:25.066 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 06:58:25.086 [info] > git status -z -uall [11ms]
2025-06-03 06:58:25.088 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 06:58:35.797 [info] > git config --get commit.template [15ms]
2025-06-03 06:58:35.799 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 06:58:35.838 [info] > git status -z -uall [15ms]
2025-06-03 06:58:35.841 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [3ms]
2025-06-03 07:00:48.240 [info] > git config --get commit.template [12ms]
2025-06-03 07:00:48.241 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 07:00:48.265 [info] > git status -z -uall [11ms]
2025-06-03 07:00:48.267 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 07:06:59.544 [info] > git config --get commit.template [8ms]
2025-06-03 07:06:59.545 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 07:06:59.568 [info] > git status -z -uall [10ms]
2025-06-03 07:06:59.569 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 07:07:11.728 [info] > git config --get commit.template [12ms]
2025-06-03 07:07:11.729 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 07:07:11.760 [info] > git status -z -uall [14ms]
2025-06-03 07:07:11.762 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 07:07:16.788 [info] > git config --get commit.template [8ms]
2025-06-03 07:07:16.789 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 07:07:16.810 [info] > git status -z -uall [10ms]
2025-06-03 07:07:16.811 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [1ms]
2025-06-03 07:10:12.096 [info] > git config --get commit.template [18ms]
2025-06-03 07:10:12.097 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 07:10:12.121 [info] > git status -z -uall [12ms]
2025-06-03 07:10:12.122 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 07:10:17.148 [info] > git config --get commit.template [1ms]
2025-06-03 07:10:17.167 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 07:10:17.186 [info] > git status -z -uall [7ms]
2025-06-03 07:10:17.187 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [2ms]
2025-06-03 07:10:22.216 [info] > git config --get commit.template [12ms]
2025-06-03 07:10:22.217 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 07:10:22.236 [info] > git status -z -uall [8ms]
2025-06-03 07:10:22.239 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 07:10:27.258 [info] > git config --get commit.template [2ms]
2025-06-03 07:10:27.273 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [2ms]
2025-06-03 07:10:27.300 [info] > git status -z -uall [14ms]
2025-06-03 07:10:27.303 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
2025-06-03 07:10:32.337 [info] > git config --get commit.template [16ms]
2025-06-03 07:10:32.338 [info] > git for-each-ref --format=%(refname)%00%(upstream:short)%00%(objectname)%00%(upstream:track)%00%(upstream:remotename)%00%(upstream:remoteref) refs/heads/main refs/remotes/main [1ms]
2025-06-03 07:10:32.365 [info] > git status -z -uall [13ms]
2025-06-03 07:10:32.368 [info] > git for-each-ref --sort -committerdate --format %(refname)%00%(objectname)%00%(*objectname) [4ms]
